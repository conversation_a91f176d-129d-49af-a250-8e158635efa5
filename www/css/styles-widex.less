// normalize ( https://necolas.github.io/normalize.css/ )
@import (less) "base/normalize.css";
// nastavení
@import "core/settings-widex.less";
// mixiny
@import "core/mixins.less";

// border-box
.boxsizing;

// lightbox + animace ( https://github.com/dimsemenov/Magnific-Popup )
@import (less) "base/magnific-popup.css";
.mfp-fade.mfp-bg { opacity: 0; transition: all 0.15s ease-out; }
.mfp-fade.mfp-bg.mfp-ready { opacity: 0.8; }
.mfp-fade.mfp-bg.mfp-removing { opacity: 0; }
.mfp-fade.mfp-wrap .mfp-content { opacity: 0; transition: all 0.15s ease-out; }
.mfp-fade.mfp-wrap.mfp-ready .mfp-content { opacity: 1; }
.mfp-fade.mfp-wrap.mfp-removing .mfp-content { opacity: 0; }
.mfp-wrap button { &:hover, &:active, &:focus { background-color: transparent; } } // ošetření buttonu

// slider Slick ( http://kenwheeler.github.io/slick/ )
@import (less) "../js/slick/slick.css";

// základní nastavení dokumentu
@import "base/base.less";

// layout, mřížka
@media screen { @import "base/layout.less"; }
// základní definice textů, webové fonty
@import "base/text.less";
// základní definice formulářů
@import "base/forms.less";
// ikony (font-face, generováno přes Grunt.js)
@import "../fonts/icons.less";

// hlavička
@import "components/header.less";

// navigace
@import "components/nav.less";
  // produktová navigace
  @import "components/nav-product.less";

// obsah
@import "components/content.less";
  // horní část stránky
  @import "components/content-head.less";
  // obsahová část, základní vzhled
  @import "components/article.less";

// komponenty
  // tabulka
  @import "components/table.less";
  // balíkovna
  @import "components/balikovna.less";
  // tlačítka
  @import "components/btn.less";
  // drobečková navigace
  @import "components/breadcrumb.less";
  // přihlášení uživatele
  @import "components/login.less";
  // hledání v hlavičce
  @import "components/search.less";
  // odkaz na košík
  @import "components/basket.less";
  // chybové hlášky
  @import "components/alert.less";
  // štítky
  @import "components/label.less";
  // skladová dostupnost
  @import "components/stock.less";
  // stránkování
  @import "components/pagination.less";
  // kategorie produktů
  @import "components/category.less";
  // filtry zboží
  @import "components/filter.less";
  // řazení
  @import "components/sort.less";
  // ovládací prvky
  @import "components/control.less";
  // ikony
  @import "components/icon.less";
  // záložky
  @import "components/tabs.less";
  // sdílení na sociálních sítích
  @import "components/share.less";
  // modální okna
  @import "components/modal.less";
  // komentáře
  @import "components/comments.less";
  // reklama na produkty
  @import "components/product-tip.less";
  // úvodní slider
  @import "components/slider.less";
  // uživatelské strany
  @import "components/user.less";
  // novinky
  @import "components/news.less";

// produkty
  // výpis produktů
  @import "components/product.less";
  // detail produktu
  @import "components/product-detail.less";

// objednávkový proces
@import "components/order/order.less";
  // postup objednávky
  @import "components/order/order-progress.less";
  // tělo objednávky
  @import "components/order/order-content.less";
  // doplňující informace k objednávce
  @import "components/order/order-info.less";
  // ovládací tlačítka objednávky
  @import "components/order/order-controls.less";
  // dopravy a platby
  @import "components/order/order-delivery.less";

// patička
@import "components/footer.less";

// AJAX podpora
@import "components/ajax.less";

// tisková verze
@import "base/print.less";
