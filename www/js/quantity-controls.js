/**
 * Quantity controls for basket
 */

// Function to add plus and minus buttons to quantity inputs
function addQuantityControls() {
  // Remove existing plus and minus buttons
  $('.control--plus, .control--minus').remove();

  // Add plus and minus buttons to all quantity inputs
  $('.control--count').each(function() {
    $(this).append(' <span class="control control--plus"><i class="icon icon--plus"></i></span> <span class="control control--minus"><i class="icon icon--minus"></i></span>');
  });

  // Add click event to plus button
  $('.control--plus').off('click').on('click', function(e) {
    e.preventDefault();
    console.log('Plus button clicked (quantity-controls.js)');

    var input = $(this).parent().find('input');
    var value = parseInt(input.val()) || 0;
    input.val(value + 1);
    console.log('New quantity:', value + 1);

    // Submit form if it has ajax class
    var $form = $(this).closest('form.ajax');
    if ($form.length) {
      console.log('Submitting form via AJAX...');
      var $recalcBtn = $form.find('button[name="recalc"]');
      if ($recalcBtn.length) {
        $recalcBtn.click();
      } else {
        console.log('Recalc button not found');
      }
    }
  });

  // Add click event to minus button
  $('.control--minus').off('click').on('click', function(e) {
    e.preventDefault();
    console.log('Minus button clicked (quantity-controls.js)');

    var input = $(this).parent().find('input');
    var value = parseInt(input.val()) || 1;
    var newValue = Math.max(0, value - 1);
    input.val(newValue);
    console.log('New quantity:', newValue);

    // Submit form if it has ajax class
    var $form = $(this).closest('form.ajax');
    if ($form.length) {
      console.log('Submitting form via AJAX...');
      var $recalcBtn = $form.find('button[name="recalc"]');
      if ($recalcBtn.length) {
        $recalcBtn.click();
      } else {
        console.log('Recalc button not found');
      }
    }
  });
}

// Initialize quantity controls on document ready
$(document).ready(function() {
  addQuantityControls();
});

// Initialize quantity controls after AJAX update
if (typeof naja !== 'undefined') {
  naja.addEventListener('complete', function() {
    setTimeout(function() {
      addQuantityControls();
    }, 100);
  });
}
