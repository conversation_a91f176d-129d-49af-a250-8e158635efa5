/**
 * Quantity controls for basket
 */

// Function to add plus and minus buttons to quantity inputs
function addQuantityControls() {
  // Remove existing plus and minus buttons
  $('.control--plus, .control--minus').remove();
  
  // Add plus and minus buttons to all quantity inputs
  $('.control--count').each(function() {
    $(this).append(' <span class="control control--plus"><i class="icon icon--plus"></i></span> <span class="control control--minus"><i class="icon icon--minus"></i></span>');
  });
  
  // Add click event to plus button
  $('.control--plus').off('click').on('click', function() {
    var input = $(this).parent().find('input');
    var value = parseFloat(input.val());
    if (isNaN(value) || value < 0) value = 0;
    input.val(value + 1);
    
    // Submit form if it has ajax class
    if ($(this).closest('form.ajax').length) {
      $('#frm-basketForm-recalc').click();
    }
  });
  
  // Add click event to minus button
  $('.control--minus').off('click').on('click', function() {
    var input = $(this).parent().find('input');
    var value = parseFloat(input.val());
    if (isNaN(value) || value <= 1) value = 2;
    input.val(value - 1);
    
    // Submit form if it has ajax class
    if ($(this).closest('form.ajax').length) {
      $('#frm-basketForm-recalc').click();
    }
  });
}

// Initialize quantity controls on document ready
$(document).ready(function() {
  addQuantityControls();
});

// Initialize quantity controls after AJAX update
if (typeof naja !== 'undefined') {
  naja.addEventListener('complete', function() {
    setTimeout(function() {
      addQuantityControls();
    }, 100);
  });
}
