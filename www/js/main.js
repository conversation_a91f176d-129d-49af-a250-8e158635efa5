// Initialize all UI components
function initUI() {

  // obecne
  var bodyWidth = window.innerWidth;

  // při resize okna
  $(window).resize(function () {

    // osetreni, zda se velikost zmenila
    if ( bodyWidth !== window.innerWidth ) {
      mobileMenu();
    }

  });

  // mobilní menu
  function mobileMenu()
  {

    // šířka okna
    var bodyWidth = window.innerWidth;
    // breakpoint mobilního menu
    var respMenuWidth = 768;

    if ( bodyWidth < respMenuWidth ) {
      $('.nav').addClass('nav--mobile');
      $('.nav-product').not('.nav-product--hidden').hide();
    }
    else {
      $('.nav').removeClass('nav--mobile');
      $('.nav-product').not('.nav-product--hidden').show();
    }

    // zobrazení menu
    $('.nav--mobile').click(function() {
      $('.nav-product').toggle();
      return false;
    });

  }
  mobileMenu();

  // taby
  if ( $('.tabs').length ) {

    $('.tabs__content > div').not('.is-active').hide();
    $('.tabs__name').on('click', function( event ) {
      event.preventDefault();

      var target = $(this).attr('href');

      $('.tabs__content > .is-active').removeClass('is-active').hide();
      $(target).addClass('is-active').show();

      $('.tabs__name').removeClass('is-active');
      $(this).addClass('is-active');
    });

  }

  // otevřít tab přímo
  if ( $('.open-tab').length ) {

    $('.open-tab').on('click', function( event ) {

      $('.tabs__name').removeClass('is-active');
      $('.tabs__content > .is-active').removeClass('is-active').hide();

      var target = $(this).attr('href');

      $(target).addClass('is-active').show();
      $('.tab[href="' + target + '"]').addClass('is-active').show();

    });

  }

  // hláška s možností zavření
  if ( $('.alert--close').length ) {

    // přidání ikony
    $('.alert--close').append( ' <i class="icon icon--close"></i>' );
    // zavření okna
    $('.alert--close .icon--close').on( 'click', function() {
      $(this).parent().hide();
    });

  }



  // modal okna
  if ( $('.modal').length ) {

    // otevření okna
    $('.modal--show').on( 'click', function() {

      // zjištění ID okna z atributu rel
      var modalName = '#' + $(this).attr('rel');
      var modalBody = modalName + ' .modal__body';

      // otevření konkrétního okna
      $( modalName ).show();

      // zjištění výšky okna
      var modalHeight = $(modalBody).outerHeight();

      // pokud je výška obrazovky menší než okno, resetuje se pozice
      if ( modalHeight > bodyHeight ) {
        $( modalName ).addClass('modal--reset');
      }

    });

    // zavření okna
    $('.modal__close').on( 'click', function() {
      $(this).closest('.modal').hide();
      return false;
    });

  }

  // antispam
  if ( $('.antispam').length ) {

    // zjištění ID z atributu rel
    var antispamNumber = $('.antispam').attr('data-help');

    // vypsání do inputu
    $('.antispam').find('input').val( antispamNumber );

    // skrytí celého bloku
    $('.antispam').hide();

  }

  // otevírání skrytých částí formuláře
  if ( $('.reveal').length ) {

    // skrytí částí
    $('.reveal').hide();

    // standardní odkaz (A) nebo jakýkoliv blok
    $('.reveal--show').not('label.reveal--show, input.reveal--show').on( 'click', function() {

      // zjištění ID z atributu rel
      var revealName = '#' + $(this).attr('rel');

      // zobrazení skryté části
      $( revealName ).toggle();

      return false;

    });

    // formulářový prvek...
    $('label.reveal--show, input.reveal--show').on( 'change', function() {

      // zjištění ID z atributu rel
      var revealName = '#' + $(this).attr('rel');

      // zobrazení skryté části
      $( revealName ).toggle();

    });

  }

  // vytisknout
  if ( $('.control--print').length ) {

    $(".control--print").click(function(){
      window.print();
      return false;
    });

  }

  // slider (Slick)
  if ( bodyWidth > 576 && $('.slider').length ) {

    $('.slider__body').slick({
      dots: true,
      speed: 500,
      slidesToShow: 1,
      autoplay: true,
      arrows: false
    });

  }

  // lightbox (Magnific Popup)
  if ( $('.gallery').length ) {

    // český překlad
    $.extend(true, $.magnificPopup.defaults, {
      tClose: 'Zavřít',
      tLoading: 'Nahrávám...',
      gallery: {
        tPrev: 'Předchozí',
        tNext: 'Následující',
        tCounter: '%curr% z %total%'
      },
      image: {
        tError: '<a href="%url%">Obrázek</a> nelze načíst.'
      },
      ajax: {
        tError: '<a href="%url%">Obsah</a> nelze načíst.'
      }
    });

    $('.gallery').magnificPopup({
      delegate: 'a',
      type: 'image',
      removalDelay: 300,
      mainClass: 'mfp-fade',
      gallery: {
        enabled: true,
        tCounter: ''
      }
    });

  }

}

// Initialize on document ready
$(document).ready(function() {
  initUI();
});

// Initialize after NAJA snippets are updated
document.addEventListener('DOMContentLoaded', function() {
  if (typeof naja !== 'undefined') {
    naja.snippetHandler.addEventListener('afterUpdate', function() {
      initUI();
    });
  }
});

// lazy loading obrázků
$(window).load(function() {

  if ( $('.product__image').length ) {
    $('.product__image img').unveil(200);
  }

});

// Přičítání a odčítání počtu kusů
function initQuantityControls() {
  console.log('Initializing quantity controls...');

  // Počítání počtu kusů - plus
  $('.control--plus').off('click').on('click', function(e) {
    e.preventDefault();
    console.log('Plus button clicked');

    // Zjištění, převod na číslo a ošetření
    var $input = $(this).siblings('input[type="text"]');
    var quantity = parseInt($input.val()) || 0;

    // Přičtení
    quantity = quantity + 1;

    // Nastavení čísla
    $input.val(quantity);
    console.log('New quantity:', quantity);

    // Automatické odeslání formuláře
    var $form = $(this).closest('form.ajax');
    if ($form.length) {
      console.log('Submitting form via AJAX...');
      var $recalcBtn = $form.find('button[name="recalc"]');
      if ($recalcBtn.length) {
        $recalcBtn.click();
      }
    }
  });

  // Odečítání počtu kusů - minus
  $('.control--minus').off('click').on('click', function(e) {
    e.preventDefault();
    console.log('Minus button clicked');

    // Zjištění, převod na číslo a ošetření
    var $input = $(this).siblings('input[type="text"]');
    var quantity = parseInt($input.val()) || 1;

    // Odečtení (minimálně 0)
    quantity = Math.max(0, quantity - 1);

    // Nastavení čísla
    $input.val(quantity);
    console.log('New quantity:', quantity);

    // Automatické odeslání formuláře
    var $form = $(this).closest('form.ajax');
    if ($form.length) {
      console.log('Submitting form via AJAX...');
      var $recalcBtn = $form.find('button[name="recalc"]');
      if ($recalcBtn.length) {
        $recalcBtn.click();
      }
    }
  });
}

// Inicializace ovládání počtu kusů při načtení stránky
$(document).ready(function() {
  initQuantityControls();
});
