# Docker commands
up:
	docker compose up -d

down:
	docker compose down

stop:
	docker compose stop

restart:
	docker compose restart

logs:
	docker compose logs -f

# Build and start containers
build:
	docker compose build
	docker compose up -d

# Cache management
cache-clear:
	docker compose exec web php /var/www/html/www/cc.php k=lZwJIL

# Alternative cache clear using direct file removal
cache-clear-direct:
	docker compose exec web rm -rf /var/www/html/temp/cache/*

# Database commands
db-shell:
	docker compose exec database mysql -u widexshopnew -pwidexshopnew widexshopnew

# Web container shell access
shell:
	docker compose exec web bash

# Show running containers
ps:
	docker compose ps

# Clean up everything (containers, volumes, images)
clean:
	docker compose down -v --rmi all

# Help
help:
	@echo "Available commands:"
	@echo "  up              - Start containers in background"
	@echo "  down            - Stop and remove containers"
	@echo "  stop            - Stop containers"
	@echo "  restart         - Restart containers"
	@echo "  logs            - Show container logs"
	@echo "  build           - Build and start containers"
	@echo "  cache-clear     - Clear application cache using cc.php"
	@echo "  cache-clear-direct - Clear cache by removing files directly"
	@echo "  db-shell        - Access database shell"
	@echo "  shell           - Access web container shell"
	@echo "  ps              - Show running containers"
	@echo "  clean           - Remove all containers, volumes and images"
	@echo "  help            - Show this help"

.PHONY: up down stop restart logs build cache-clear cache-clear-direct db-shell shell ps clean help
