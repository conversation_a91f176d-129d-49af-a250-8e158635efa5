{snippet pageHeader}
{*<!-- h<PERSON><PERSON><PERSON><PERSON> start -->*}
<header class="header" role="banner">

  <div class="container-fluid">

    {*<!-- logo start -->*}
    <div class="header__logo">

      {if $presenter->isLinkCurrent('Homepage:default')}
      <img src="{$baseUri}/img/logo-{$appConfig["serverCode"]}.png" alt="{$presenter->config["SERVER_NAME"]}">
      {else}
      <a href="{$baseUri}" title="{$presenter->config["SERVER_NAME"]}"><img src="{$baseUri}/img/logo-{$appConfig["serverCode"]}.png" alt="{$presenter->config["SERVER_NAME"]}"></a>
      {/if}

    </div>
    {*<!-- logo end -->*}

    {*<!-- vyhledávání start -->*}
    <div class="search">

      {form searchForm}
        {input fulltext 'id'=>'bfulltext', 'class'=>'search__input', placeholder => 'Hledej...'}
        <button type="submit" name="quickSearch" id="frm-searchForm-quickSearch" value="Hledat" class="search__submit"><i class="icon icon--search"></i> Vyhledat</button>
      {/form searchForm}

    </div>
    {*<!-- vyhledávání end -->*}

    {*<!-- košík start -->*}
    <div class="basket" id="snippet-basketWindow">

    {snippet basketWindow}
    <!-- Snippet basketWindow start -->

      {if $basketItemsCnt > 0}
        <a href="{plink Basket:default}"><i class="icon icon--basket"></i></a>
        <p class="basket__header">{_'Košík'}</p>
        <p class="basket__content">
          <a href="{plink Basket:default}"><strong>{$basketItemsCnt}&nbsp;{_'ks'}</strong></a>
          {_'za'}
          <a href="{plink Basket:default}"><strong>{$basketPriceSum|formatPrice}</strong></a>
        </p>
      {else}
        <i class="icon icon--basket"></i>
        <p class="basket__header">{_'Košík'}</p>
        <p class="basket__content">{_'je prázdný'}</p>
      {/if}

    <!-- Snippet basketWindow end -->
    {/snippet}

    </div>
    {*<!-- košík end -->*}

    {*<!-- přihlášení uživatele start -->*}
    <div class="login">

      <i class="icon icon--man"></i>

      <p class="login__header">{_'Přihlášení'}</p>

      {if $userRow->usrid > 0}
      <p class="login__content">
        <a href="{plink User:default}">{if !empty($userRow->usriname)}{$userRow->usriname} {$userRow->usrilname}{else}{$userRow->usrmail}{/if}</a>
        <a href="{plink User:logout}">{_'Odhlásit'}</a>
      </p>
      {else}
      <p class="login__content">
        <a href="{plink User:login}">{_'přihlášení'}</a> <span>-</span>
        <a href="{plink User:add}">{_'registrace'}</a>
      </p>
      {/if}

    </div>
    {*<!-- přihlášení uživatele end -->*}

    {*<!-- hlavní menu start -->*}
    {include @pageMenuTop.latte}
    {*<!-- hlavní menu end -->*}

  </div>

</header>
{*<!-- hlavička end -->*}
{/snippet}
