{default $urlkey => ''}
{default $lng    => $lang}

{default pageTitle       => $presenter->config["INDEX_TITLE"]}
{default pageDescription => $presenter->config["INDEX_DESC"]}
{default pageImage       => $baseUri."/img/logo.jpg"}

{* DOCTYPE a <head></head> *}
{include @pageHead.latte pageTitle => $pageTitle, pageDescription => $pageDescription, pageImage => $pageImage}

<body>
{* Page Header *}
{snippetArea pageHeader}
{include @pageHeader.latte}
{/snippetArea}
  {if $presenter->isLinkCurrent("Homepage:default")}
    {block #homepageSlider}
      {* plni se z Homepage.default.late *}
    {/block}
  {else}
  <div class="content-head">

    <div class="container-fluid">

      <div class="row">

        <div class="col-xs-12">

          <h1 class="content-head__title">{$pageTitle}</h1>

          {*<!-- drobečková navigace start -->*}
          <div class="breadcrumb">

            <a href="{plink Homepage:default}">Úvod</a>

            {if !empty($breadcrumbs)}
              {foreach $breadcrumbs as $breadcrumb}
                <i class="icon icon--dash"></i>
                {if $iterator->last}
                  <strong>{$breadcrumb["text"]}</strong>
                {else}
                  <a href="{$breadcrumb["url"]}">{$breadcrumb["text"]}</a>
                {/if}
              {/foreach}
            {else}
              <i class="icon icon--dash"></i> <strong>{$pageTitle}</strong>
            {/if}

          </div>
          {*<!-- drobečková navigace end -->*}

        </div>

      </div>

    </div>

  </div>
  {/if}

      {* navigace objednávkovým procesem - plní se v příslušné šabloně *}
      {block #orderProgress}
      {/block}

  <div class="container-fluid">

    <div class="row">

      {* <!-- produktové menu start --> *}
      <div class="col-sm-4 col-md-3 nav-product__wrapper">
        {include @pageMenuProducts.latte}
      </div>
      {* <!-- produktové menu end --> *}



      {if !$showMenuLeft}
      <div class="col-xs-12">
      {else}
      <div class="col-sm-8 col-md-9">
      {/if}

        {*<!-- obsah start -->*}
        <main class="content" role="main">

          {*<!-- chybové hlášky start -->*}
          {foreach $flashes as $flash}
            <div class="alert alert--{$flash->type}" role="alert"><p>{$flash->message}</p></div>
          {/foreach}
          {*<!-- chybové hlášky end -->*}

          {include #content}

        </main>
        {*<!-- obsah end -->*}

      </div>

    </div>

  </div>
  {block #pagination}
    {control paginator}
  {/block}

  {* reklama na produkty *}
  {include @pageFooterAdd.latte}

  {* patička *}
  {include @pageFooter.latte}

  <script>
    WebFontConfig = {
      google: { families: [ 'Open+Sans:300,400,700:latin,latin-ext' ] }
    };
  </script>
  <script src="{$baseUri}/js/webfont.js" async defer></script>

  <script src="{$baseUri}/js/jquery-1.12.4.min.js"></script>
  <script src="{$baseUri}/js/scripts.js"></script>
  {if $presenter->isLinkCurrent('Homepage:default')}<script src="{$baseUri}/js/slick/slick.min.js"></script>{/if}
  <script src="https://www.google.com/recaptcha/api.js"></script>

  {* Našeptávání adres pomocí Mapy.cz API - načítáme jen na stránce s kontaktními údaji *}
  {if $presenter->isLinkCurrent('Basket:orderContact')}<script src="{$baseUri}/js/mapycz-suggest.js"></script>{/if}

  {* podpora ajaxu - NAJA *}
  <script src="{$baseUri}/js/naja.min.js"></script>
  <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
      naja.initialize();

      // Debug AJAX requests
      naja.addEventListener('start', function(event) {
        console.log('AJAX request started', event);
      });

      naja.addEventListener('success', function(event) {
        console.log('AJAX request successful', event);
        // Debug snippets
        console.log('Snippets:', event.detail.payload.snippets);
        // Reinitialize JavaScript after AJAX update
        setTimeout(function() {
          // Nejprve inicializujeme ovládání počtu kusů
          if (typeof initQuantityControls === 'function') {
            console.log('Initializing quantity controls...');
            initQuantityControls();
          }
          // Potom inicializujeme ostatní UI komponenty
          if (typeof initUI === 'function') {
            console.log('Initializing UI...');
            initUI();
          }
        }, 100);
      });

      naja.addEventListener('error', function(event) {
        console.error('AJAX request failed', event);
      });
    });
  </script>

  {if $presenter->isLinkCurrent('Basket:orderDelMode')}
  <script>

  function number_format(num) {
    return parseInt( num ).toLocaleString('cs-CZ');
  }

  $("input:radio[name='orddelid']").change(function() {
    var payModeName = '';
    var payModePrice = 0;
    var payModeName = '';
    var delModePrice = 0;
    var priceSum = {$priceSumVat};
    var delid = $(this).val();
    {foreach $payModesJs as $pay}
      {if isset($delModes[$pay->delmasid])}
      if (delid == {$pay->delid|noescape}) {
    payModeName = {$pay->delname};
    payModePrice = {$pay->delprice|noescape};
    delModeName = {$delModes[$pay->delmasid]->delname};
    delModePrice = {$delModes[$pay->delmasid]->delprice|noescape};
      }
      {/if}
    {/foreach}

    delPrice = delModePrice + payModePrice;
    priceSum = priceSum + delPrice;
    $( "#priceSumTotalVat" ).html(number_format(priceSum)+' '+{$presenter->curCodes[$presenter->curId]});
    $( "#priceDeliveryVat" ).html(number_format(delPrice)+' '+{$presenter->curCodes[$presenter->curId]});
    $( "#deliveryName" ).html(delModeName+', '+payModeName);
  })

  </script>
  {/if}
</body>
</html>

{block #blockFooter}
{/block}
